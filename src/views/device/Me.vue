<script setup>
import { onMounted, ref, computed } from 'vue'
import * as echarts from 'echarts'
import '@/utils/date.js'



// 静态套餐信息数据
const packageInfo = ref({
  name: '30元500M套餐',
  totalFlow: 500 * 1024 * 1024, // 500MB转换为字节
  usedFlow: 320 * 1024 * 1024,  // 320MB转换为字节
  remainingFlow: 180 * 1024 * 1024, // 180MB转换为字节
  expireDate: '2024-12-31',
  status: 'active'
})

// 静态流量使用趋势数据
const usageTrend = ref([
  { date: '2024-08-30', usage: 45 * 1024 * 1024 },
  { date: '2024-08-31', usage: 52 * 1024 * 1024 },
  { date: '2024-09-01', usage: 38 * 1024 * 1024 },
  { date: '2024-09-02', usage: 61 * 1024 * 1024 },
  { date: '2024-09-03', usage: 43 * 1024 * 1024 },
  { date: '2024-09-04', usage: 55 * 1024 * 1024 },
  { date: '2024-09-05', usage: 48 * 1024 * 1024 }
])

// 图表实例
let chart = null

// 加载状态
const loading = ref(false)

// 计算使用百分比
const usagePercentage = computed(() => {
  if (packageInfo.value.totalFlow === 0) return 0
  return Math.round((packageInfo.value.usedFlow / packageInfo.value.totalFlow) * 100)
})

// 格式化流量显示
const formatFlow = (bytes) => {
  if (bytes === 0) return '0 MB'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 绘制流量趋势图
const drawChart = () => {
  if (!chart) return
  
  const dates = usageTrend.value.map(item => item.date)
  const values = usageTrend.value.map(item => item.usage)
  
  const option = {
    title: {
      text: '最近7天流量使用趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const data = params[0]
        return `${data.axisValue}<br/>流量使用: ${formatFlow(data.value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        formatter: function(value) {
          return new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return formatFlow(value)
        }
      }
    },
    series: [{
      name: '流量使用',
      type: 'line',
      data: values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#1890ff'
      },
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(24, 144, 255, 0.05)'
          }]
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chart.setOption(option)
}



// 模拟加载套餐信息（使用静态数据）
const loadPackageInfo = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 400))
  console.log('套餐信息已加载（静态数据）')
}

// 模拟加载流量使用趋势（使用静态数据）
const loadUsageTrend = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200))
  console.log('流量趋势已加载（静态数据）')
}

// 刷新数据（模拟刷新）
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadPackageInfo(),
      loadUsageTrend()
    ])

    // 模拟数据更新（可以在这里随机更新一些数据）
    packageInfo.value.usedFlow = Math.floor(Math.random() * 100 + 250) * 1024 * 1024
    packageInfo.value.remainingFlow = packageInfo.value.totalFlow - packageInfo.value.usedFlow

    // 重新绘制图表
    setTimeout(() => {
      drawChart()
    }, 100)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 初始化图表
  chart = echarts.init(document.getElementById('usage-chart'))
  
  // 加载数据
  await refreshData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
})
</script>

<template>
  <div class="me-container">
    <a-spin :spinning="loading">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 刷新按钮 -->
        <div style="text-align: right; margin-bottom: 16px;">
          <a-button type="primary" @click="refreshData" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新数据
          </a-button>
        </div>

        <!-- 套餐信息卡片 -->
        <a-card title="我的套餐">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐名称"
                :value="packageInfo.name"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="到期时间"
                :value="packageInfo.expireDate"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐状态"
                value="正常使用"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>

        <!-- 流量使用情况卡片 -->
        <a-card title="流量使用情况">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="总流量"
                :value="formatFlow(packageInfo.totalFlow)"
                :value-style="{ color: '#1890ff' }"
              >
                <template #suffix>
                  <CloudOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="已使用"
                :value="formatFlow(packageInfo.usedFlow)"
                :value-style="{ color: '#ff4d4f' }"
              >
                <template #suffix>
                  <DownloadOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="剩余流量"
                :value="formatFlow(packageInfo.remainingFlow)"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <UploadOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
          
          <!-- 使用进度条 -->
          <div style="margin-top: 24px">
            <div style="margin-bottom: 8px">
              <span>使用进度: {{ usagePercentage }}%</span>
            </div>
            <a-progress 
              :percent="usagePercentage" 
              :stroke-color="usagePercentage > 80 ? '#ff4d4f' : usagePercentage > 60 ? '#faad14' : '#52c41a'"
            />
          </div>
        </a-card>

        <!-- 流量使用趋势图 -->
        <a-card>
          <div id="usage-chart" style="width: 100%; height: 400px;"></div>
        </a-card>
      </a-space>
    </a-spin>
  </div>
</template>

<style scoped>
.me-container {
  padding: 24px;
}



@media (max-width: 768px) {
  .me-container {
    padding: 16px;
  }
}
</style>
