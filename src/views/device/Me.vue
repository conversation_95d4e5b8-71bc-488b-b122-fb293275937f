<script setup>
import { onMounted, ref, computed } from 'vue'
import * as echarts from 'echarts'
import '@/utils/date.js'
import {
  ReloadOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  CloudOutlined,
  DownloadOutlined,
  UploadOutlined,
  WechatOutlined,
  AlipayCircleOutlined
} from '@ant-design/icons-vue'



// 静态套餐信息数据
const packageInfo = ref({
  name: '30元500M套餐',
  totalFlow: 500 * 1024 * 1024, // 500MB转换为字节
  usedFlow: 320 * 1024 * 1024,  // 320MB转换为字节
  remainingFlow: 180 * 1024 * 1024, // 180MB转换为字节
  expireDate: '2024-12-31',
  status: 'active'
})

// 充值相关数据
const rechargeVisible = ref(false)
const paymentVisible = ref(false)
const selectedPackage = ref(null)
const selectedPayment = ref(null)

// 可选套餐列表（参考RentalEditor.vue）
const packages = ref([{
  no: 1,
  name: '10元100M套餐',
  price: 10,
  flow: '100M',
  expireTime: '30天',
  _selected: false
}, {
  no: 2,
  name: '30元500M套餐',
  price: 30,
  flow: '500M',
  expireTime: '30天',
  _selected: false
}, {
  no: 3,
  name: '8元套餐',
  price: 8,
  flow: '50M',
  expireTime: '15天',
  _selected: false
}, {
  no: 4,
  name: '20元套餐',
  price: 20,
  flow: '200M',
  expireTime: '30天',
  _selected: false
}])

// 支付方式列表（参考RentalEditor.vue）
const payments = ref([{
  id: 'WEIXIN-NATIVE',
  name: '微信',
  icon: 'WechatOutlined',
  color: '#87d068',
  _selected: false
}, {
  id: 'ALIPAY-WAP',
  name: '支付宝',
  icon: 'AlipayCircleOutlined',
  color: '#1677ff',
  _selected: false
}])

// 静态流量使用趋势数据
const usageTrend = ref([
  { date: '2024-08-30', usage: 45 * 1024 * 1024 },
  { date: '2024-08-31', usage: 52 * 1024 * 1024 },
  { date: '2024-09-01', usage: 38 * 1024 * 1024 },
  { date: '2024-09-02', usage: 61 * 1024 * 1024 },
  { date: '2024-09-03', usage: 43 * 1024 * 1024 },
  { date: '2024-09-04', usage: 55 * 1024 * 1024 },
  { date: '2024-09-05', usage: 48 * 1024 * 1024 }
])

// 图表实例
let chart = null

// 加载状态
const loading = ref(false)

// 计算使用百分比
const usagePercentage = computed(() => {
  if (packageInfo.value.totalFlow === 0) return 0
  return Math.round((packageInfo.value.usedFlow / packageInfo.value.totalFlow) * 100)
})

// 格式化流量显示
const formatFlow = (bytes) => {
  if (bytes === 0) return '0 MB'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 充值相关方法
const openRecharge = () => {
  // 重置选择状态
  packages.value.forEach(pkg => pkg._selected = false)
  payments.value.forEach(payment => payment._selected = false)
  selectedPackage.value = null
  selectedPayment.value = null
  rechargeVisible.value = true
}

const selectPackage = (pkg) => {
  packages.value.forEach(p => p._selected = p.no === pkg.no)
  selectedPackage.value = pkg
}

const selectPayment = (payment) => {
  payments.value.forEach(p => p._selected = p.id === payment.id)
  selectedPayment.value = payment
}

const proceedToPayment = () => {
  if (!selectedPackage.value) {
    // 这里可以添加提示消息
    return
  }
  paymentVisible.value = true
}

const confirmPayment = () => {
  if (!selectedPackage.value || !selectedPayment.value) {
    return
  }

  // 模拟支付成功
  setTimeout(() => {
    // 更新套餐信息（模拟充值成功）
    packageInfo.value.name = selectedPackage.value.name
    packageInfo.value.totalFlow = parseInt(selectedPackage.value.flow) * 1024 * 1024
    packageInfo.value.usedFlow = 0
    packageInfo.value.remainingFlow = packageInfo.value.totalFlow

    // 关闭弹窗
    paymentVisible.value = false
    rechargeVisible.value = false

    // 重新绘制图表
    setTimeout(() => {
      drawChart()
    }, 100)
  }, 1000)
}

// 绘制流量趋势图
const drawChart = () => {
  if (!chart) return
  
  const dates = usageTrend.value.map(item => item.date)
  const values = usageTrend.value.map(item => item.usage)
  
  const option = {
    title: {
      text: '最近7天流量使用趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const data = params[0]
        return `${data.axisValue}<br/>流量使用: ${formatFlow(data.value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        formatter: function(value) {
          return new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return formatFlow(value)
        }
      }
    },
    series: [{
      name: '流量使用',
      type: 'line',
      data: values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#1890ff'
      },
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(24, 144, 255, 0.05)'
          }]
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chart.setOption(option)
}



// 模拟加载套餐信息（使用静态数据）
const loadPackageInfo = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 400))
  console.log('套餐信息已加载（静态数据）')
}

// 模拟加载流量使用趋势（使用静态数据）
const loadUsageTrend = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200))
  console.log('流量趋势已加载（静态数据）')
}

// 刷新数据（模拟刷新）
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadPackageInfo(),
      loadUsageTrend()
    ])

    // 模拟数据更新（可以在这里随机更新一些数据）
    packageInfo.value.usedFlow = Math.floor(Math.random() * 100 + 250) * 1024 * 1024
    packageInfo.value.remainingFlow = packageInfo.value.totalFlow - packageInfo.value.usedFlow

    // 重新绘制图表
    setTimeout(() => {
      drawChart()
    }, 100)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 初始化图表
  chart = echarts.init(document.getElementById('usage-chart'))
  
  // 加载数据
  await refreshData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
})
</script>

<template>
  <div class="me-container">
    <a-spin :spinning="loading">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 套餐信息卡片 -->
        <a-card title="我的套餐">
          <template #extra>
            <a-space>
              <a-button @click="refreshData" :loading="loading">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
              <a-button type="primary" @click="openRecharge">
                <template #icon>
                  <CreditCardOutlined />
                </template>
                充值
              </a-button>
            </a-space>
          </template>
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐名称"
                :value="packageInfo.name"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="到期时间"
                :value="packageInfo.expireDate"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐状态"
                value="正常使用"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>

        <!-- 流量使用情况卡片 -->
        <a-card title="流量使用情况">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="总流量"
                :value="formatFlow(packageInfo.totalFlow)"
                :value-style="{ color: '#1890ff' }"
              >
                <template #suffix>
                  <CloudOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="已使用"
                :value="formatFlow(packageInfo.usedFlow)"
                :value-style="{ color: '#ff4d4f' }"
              >
                <template #suffix>
                  <DownloadOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="剩余流量"
                :value="formatFlow(packageInfo.remainingFlow)"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <UploadOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
          
          <!-- 使用进度条 -->
          <div style="margin-top: 24px">
            <div style="margin-bottom: 8px">
              <span>使用进度: {{ usagePercentage }}%</span>
            </div>
            <a-progress 
              :percent="usagePercentage" 
              :stroke-color="usagePercentage > 80 ? '#ff4d4f' : usagePercentage > 60 ? '#faad14' : '#52c41a'"
            />
          </div>
        </a-card>

        <!-- 流量使用趋势图 -->
        <a-card>
          <div id="usage-chart" style="width: 100%; height: 400px;"></div>
        </a-card>
      </a-space>
    </a-spin>

    <!-- 充值弹窗 -->
    <a-modal
      v-model:open="rechargeVisible"
      title="选择充值套餐"
      :width="800"
      :footer="null"
    >
      <div style="margin-bottom: 16px;">
        <h4>选择套餐</h4>
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="pkg in packages"
            :key="pkg.no"
            :xs="24"
            :sm="12"
            :md="12"
            :lg="12"
          >
            <a-card
              :hoverable="true"
              :class="pkg._selected ? 'selected-package' : ''"
              @click="selectPackage(pkg)"
              size="small"
            >
              <template #title>
                {{ pkg.name }}
              </template>
              <template #extra>
                <template v-if="pkg._selected">
                  <CheckCircleOutlined style="color: #52c41a; font-size: 18px;" />
                </template>
              </template>
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="价格">
                  ¥{{ pkg.price }}
                </a-descriptions-item>
                <a-descriptions-item label="流量">
                  {{ pkg.flow }}
                </a-descriptions-item>
                <a-descriptions-item label="有效期">
                  {{ pkg.expireTime }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <div style="text-align: center; margin-top: 24px;">
        <a-space>
          <a-button @click="rechargeVisible = false">取消</a-button>
          <a-button
            type="primary"
            :disabled="!selectedPackage"
            @click="proceedToPayment"
          >
            下一步
          </a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 支付弹窗 -->
    <a-modal
      v-model:open="paymentVisible"
      title="选择支付方式"
      :width="600"
      :footer="null"
    >
      <div v-if="selectedPackage" style="margin-bottom: 24px;">
        <a-alert
          :message="`已选择套餐：${selectedPackage.name} - ¥${selectedPackage.price}`"
          type="info"
          show-icon
        />
      </div>

      <div style="margin-bottom: 16px;">
        <h4>选择支付方式</h4>
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="payment in payments"
            :key="payment.id"
            :xs="24"
            :sm="12"
            :md="12"
          >
            <a-card
              :hoverable="true"
              :class="payment._selected ? 'selected-package' : ''"
              @click="selectPayment(payment)"
              size="small"
            >
              <div style="text-align: center;">
                <a-avatar
                  :size="48"
                  :style="{ backgroundColor: payment.color, marginBottom: '8px' }"
                >
                  <template #icon>
                    <WechatOutlined v-if="payment.icon === 'WechatOutlined'" />
                    <AlipayCircleOutlined v-else-if="payment.icon === 'AlipayCircleOutlined'" />
                  </template>
                </a-avatar>
                <div>{{ payment.name }}</div>
                <CheckCircleOutlined
                  v-if="payment._selected"
                  style="color: #52c41a; font-size: 18px; margin-top: 8px;"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <div style="text-align: center; margin-top: 24px;">
        <a-space>
          <a-button @click="paymentVisible = false">返回</a-button>
          <a-button
            type="primary"
            :disabled="!selectedPayment"
            @click="confirmPayment"
          >
            确认支付
          </a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.me-container {
  padding: 24px;
}



@media (max-width: 768px) {
  .me-container {
    padding: 16px;
  }
}

.selected-package {
  border-color: #1890ff;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
</style>
